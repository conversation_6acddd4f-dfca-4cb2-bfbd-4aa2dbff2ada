// import { load } from "@std/dotenv";

// Load environment variables
// await load({ export: true });

export interface Config {
  // Server configuration
  port: number;
  host: string;
  nodeEnv: string;

  // Database configuration
  database: {
    host: string;
    port: number;
    database: string;
    username: string;
    password: string;
    maxConnections: number;
    connectionTimeoutMs: number;
    idleTimeoutMs: number;
  };

  // Redis configuration
  redis: {
    host: string;
    port: number;
    password?: string;
    database: number;
    maxRetries: number;
    retryDelayMs: number;
  };

  // JWT configuration
  jwt: {
    secret: string;
    expiresIn: string;
    issuer: string;
  };

  // CORS configuration
  cors: {
    origins: string[];
    credentials: boolean;
  };

  // Rate limiting configuration
  rateLimit: {
    windowMs: number;
    maxRequests: number;
  };

  // WebSocket configuration
  websocket: {
    enabled: boolean;
    heartbeatInterval: number;
    maxConnections: number;
  };

  // Analytics configuration
  analytics: {
    metricsInterval: number;
    batchSize: number;
    retentionDays: number;
  };

  // Logging configuration
  logging: {
    level: string;
    format: string;
  };

  // External services
  services: {
    linkTracking: string;
    dashboard: string;
    integration: string;
  };
}

// Default configuration
const defaultConfig: Config = {
  port: parseInt(Deno.env.get("PORT") || "3002"),
  host: Deno.env.get("HOST") || "0.0.0.0",
  nodeEnv: Deno.env.get("NODE_ENV") || "development",

  database: {
    host: Deno.env.get("DB_HOST") || "localhost",
    port: parseInt(Deno.env.get("DB_PORT") || "5432"),
    database: Deno.env.get("DB_NAME") || "ecommerce_analytics",
    username: Deno.env.get("DB_USER") || "postgres",
    password: Deno.env.get("DB_PASSWORD") || "password",
    maxConnections: parseInt(Deno.env.get("DB_MAX_CONNECTIONS") || "20"),
    connectionTimeoutMs: parseInt(Deno.env.get("DB_CONNECTION_TIMEOUT") || "30000"),
    idleTimeoutMs: parseInt(Deno.env.get("DB_IDLE_TIMEOUT") || "30000"),
  },

  redis: {
    host: Deno.env.get("REDIS_HOST") || "localhost",
    port: parseInt(Deno.env.get("REDIS_PORT") || "6379"),
    password: Deno.env.get("REDIS_PASSWORD"),
    database: parseInt(Deno.env.get("REDIS_DB") || "0"),
    maxRetries: parseInt(Deno.env.get("REDIS_MAX_RETRIES") || "3"),
    retryDelayMs: parseInt(Deno.env.get("REDIS_RETRY_DELAY") || "1000"),
  },

  jwt: {
    secret: Deno.env.get("JWT_SECRET") || "your-secret-key",
    expiresIn: Deno.env.get("JWT_EXPIRES_IN") || "24h",
    issuer: Deno.env.get("JWT_ISSUER") || "analytics-service",
  },

  cors: {
    origins: (Deno.env.get("CORS_ORIGINS") || "http://localhost:3000,http://localhost:3001").split(","),
    credentials: Deno.env.get("CORS_CREDENTIALS") === "true",
  },

  rateLimit: {
    windowMs: parseInt(Deno.env.get("RATE_LIMIT_WINDOW_MS") || "900000"), // 15 minutes
    maxRequests: parseInt(Deno.env.get("RATE_LIMIT_MAX_REQUESTS") || "1000"),
  },

  websocket: {
    enabled: Deno.env.get("WEBSOCKET_ENABLED") !== "false",
    heartbeatInterval: parseInt(Deno.env.get("WEBSOCKET_HEARTBEAT_INTERVAL") || "30000"),
    maxConnections: parseInt(Deno.env.get("WEBSOCKET_MAX_CONNECTIONS") || "1000"),
  },

  analytics: {
    metricsInterval: parseInt(Deno.env.get("ANALYTICS_METRICS_INTERVAL") || "5000"),
    batchSize: parseInt(Deno.env.get("ANALYTICS_BATCH_SIZE") || "100"),
    retentionDays: parseInt(Deno.env.get("ANALYTICS_RETENTION_DAYS") || "365"),
  },

  logging: {
    level: Deno.env.get("LOG_LEVEL") || "INFO",
    format: Deno.env.get("LOG_FORMAT") || "json",
  },

  services: {
    linkTracking: Deno.env.get("LINK_TRACKING_SERVICE_URL") || "http://link-tracking:8080",
    dashboard: Deno.env.get("DASHBOARD_SERVICE_URL") || "http://dashboard:3000",
    integration: Deno.env.get("INTEGRATION_SERVICE_URL") || "http://integration:3001",
  },
};

export const config = defaultConfig;
